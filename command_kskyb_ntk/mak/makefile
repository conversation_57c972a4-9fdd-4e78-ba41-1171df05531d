#Copyright 26 Aug 2004 KskyB,Inc. All Rights Reserved

CC=gcc
LINT=lint
COPY=cp
CFLAG=-std=gnu99 -Wall -g
CXXFLAG=-std=gnu++11 -Wall -g
LIBS=-lrt

ORG_D=${HOME}/command_kskyb_ntk
BIN_D=${ORG_D}/bin
CFG_D=${ORG_D}/cfg
OBJ_D=${ORG_D}/obj
INC_D=${ORG_D}/inc
SRC_D=${ORG_D}/src

INCLUDE=-I$(INC_D)

SMS_COMMAND=${ORG_D}/bin/cmd
SMS_MONITOR=${ORG_D}/bin/mnt
SMS_LOGGER=${ORG_D}/bin/log
SMS_WATCHDOG=${ORG_D}/bin/dog
CREATE_TABLE=${ORG_D}/cfg/crt

#########################################################

all	: $(CREATE_TABLE) $(SMS_COMMAND) $(SMS_MONITOR)	$(SMS_LOGGER) $(SMS_WATCHDOG)

create_table	:	$(CREATE_TABLE)
sms_command		:	$(SMS_COMMAND)
sms_monitor		:	$(SMS_MONITOR)
sms_logger		:	$(SMS_LOGGER)
sms_watchdog	:	$(SMS_WATCHDOG)

#########################################################

$(CREATE_TABLE): $(OBJ_D)/create_table.o
	${CC} $(CFLAG) $(OBJ_D)/create_table.o ${INCLUDE} -o $@

$(SMS_COMMAND): $(OBJ_D)/sms_command.o
	${CC} $(CFLAG) $(OBJ_D)/sms_command.o ${INCLUDE} -o $@

$(SMS_MONITOR): $(OBJ_D)/sms_monitor.o $(OBJ_D)/sms_ctrlsub.o
	${CC} $(CFLAG) $(LIBS) $(OBJ_D)/sms_monitor.o $(OBJ_D)/sms_ctrlsub.o ${INCLUDE} -o $@

$(SMS_LOGGER): $(OBJ_D)/sms_logger.o $(OBJ_D)/sms_ctrlsub.o
	${CC} $(CFLAG) $(LIBS) $(OBJ_D)/sms_logger.o $(OBJ_D)/sms_ctrlsub.o ${INCLUDE} -o $@

$(SMS_WATCHDOG): $(OBJ_D)/sms_watchdog.o $(OBJ_D)/sms_ctrlsub.o
	${CC} $(CFLAG) $(LIBS) $(OBJ_D)/sms_watchdog.o $(OBJ_D)/sms_ctrlsub.o ${INCLUDE} -o $@

#######################################################

$(OBJ_D)/create_table.o : $(SRC_D)/create_table.c
	${CC} -o $@ $(CFLAG) ${INCLUDE} -c $(SRC_D)/create_table.c  

$(OBJ_D)/sms_command.o : $(SRC_D)/sms_command.c
	${CC} -o $@ $(CFLAG) ${INCLUDE} -c $(SRC_D)/sms_command.c  

$(OBJ_D)/sms_monitor.o : $(SRC_D)/sms_monitor.c
	${CC} -o $@ $(CFLAG) ${INCLUDE} -c $(SRC_D)/sms_monitor.c  

$(OBJ_D)/sms_logger.o : $(SRC_D)/sms_logger.c
	${CC} -o $@ $(CFLAG) ${INCLUDE} -c $(SRC_D)/sms_logger.c  

$(OBJ_D)/sms_watchdog.o : $(SRC_D)/sms_watchdog.c
	${CC} -o $@ $(CFLAG) ${INCLUDE} -c $(SRC_D)/sms_watchdog.c  

$(OBJ_D)/sms_ctrlsub.o : $(SRC_D)/sms_ctrlsub.c
	${CC} -o $@ $(CFLAG) ${INCLUDE} -c $(SRC_D)/sms_ctrlsub.c
	g++ -o $(OBJ_D)/sms_ctrlsub++.o $(CXXFLAG) ${INCLUDE} -c $(SRC_D)/sms_ctrlsub.c

clean:
	rm -rf core $(OBJ_D)/*.o $(SMS_COMMAND) $(SMS_LOGGER) $(SMS_MONITOR) $(SMS_WATCHDOG) $(CREATE_TABLE)
