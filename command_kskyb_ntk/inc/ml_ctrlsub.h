/*
 * Copyright (c) (주)메타랜드
 *
 * Program Name : ml_ctrlsub.h
 * Comments     : 메타랜드 통합 쇼핑몰 관리를 위한
 *                내부 통신 관련 Module들
 * 
 * ----------------------------------------------------------------------------
 * History
 *    [ 1] Initial Coding 97.12.30 InMok,Song
 */

extern int  ml_sub_init         (   char * pm_process_no,
                                    char * pm_process_name,
                                    char * pm_shm_info,
                                    char * pm_group_shm_info);

extern int  ml_sub_send_moni    (   char * pm_send_buffer,
                                    int    pm_send_length,
                                    int    pm_wait_sec,
                                    int    pm_status_code,
                                    int    pm_os_error_no);

extern int  ml_sub_send_log     (   char * pm_send_buffer,
                                    int    pm_send_length,
                                    int    pm_wait_sec,
                                    int    pm_status_code,
                                    int    pm_os_error_no);

extern int  ml_sub_send_group    (  int    pm_msg_type,
                                    int    pm_msg_sub_type,
                                    char * pm_send_buffer,
                                    int    pm_send_length,
                                    int    pm_q_mtype,
                                    int    pm_wait_sec);

extern int  ml_sub_send_process  (  int    pm_process_no,
                                    int    pm_msg_type,
                                    int    pm_msg_sub_type,
                                    char * pm_send_buffer,
                                    int    pm_send_length,
                                    int    pm_q_mtype,
                                    int    pm_wait_sec);

extern int  ml_sub_recv_all     (   char * pm_recv_buffer,
                                    int    pm_buffer_length,
                                    int    pm_wait_sec);

extern int  ml_sub_recv_select  (   int    pm_mtype,
                                    char * pm_recv_buffer,
                                    int    pm_buffer_length,
                                    int    pm_wait_sec);

extern int  ml_sub_end          (void);
extern int sndCheck2(char* process_no,int flag);

extern int Alert2Admin(char* pForm,...);
extern int strReplace(char* sourceMsg , char* a , char* b);


/* END */
