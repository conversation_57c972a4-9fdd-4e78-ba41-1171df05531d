/*
 * Program Name   : shm_info.h
 * Remarks        : Shared Memory Information LAYOUT
 * -----------------------------------------------------------------------------
 * History
 *
 *      1) Initial Coding 97.12.31 IM,Song
 */

#ifndef __SHM_INFO_H__
#define __SHM_INFO_H__

#ifdef __cplusplus
extern "C" {
#endif

struct _sys_info
{
    key_t           shm_key;
    key_t           sema_key;
    long            business_date;
    int             max_process; /* 현재 수용된 프로세스의 최대 수 */
};

struct _process_info
{
    char            process_no          [  7];
    char            process_name        [ 32];
    char            filler_1            [  1];
    int             os_process_no;
    char            execute_directory   [256];
    char            execute_program     [256];
    short int       process_type;
    short int       priority;
    short int       process_status;
    short int       filler_2;
    int             monitor_process_no;
    int             logger_process_no;
    int             send_group_no;
    key_t           my_q_key;
    key_t           shm_key;
    key_t           sema_key;
    long            start_time;
    long            stop_time;
    long            check_time;
    int             ProcessCheckFlag;    
    char            filler_3            [ 40];
};

struct _shm_info
{
    struct  _sys_info           sys_info;
    struct  _process_info       process_info[SYS_MAX_PROCESS];
};


#ifdef __cplusplus
}
#endif

#endif /*  __SHM_INFO_H__  */

/* END */
