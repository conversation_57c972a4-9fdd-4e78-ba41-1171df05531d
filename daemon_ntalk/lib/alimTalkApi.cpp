#include "alimTalkApi.h"
#include <iconv.h>
#include <string.h>
#include <sys/time.h>
#include <time.h>

char *replaceAll(char *s, const char *olds, const char *news);
void RemoveFirst(char *buf);
void RemoveEnd(char *buf);

void CAlimtalkApi::makeSmsRequestMsg(const vector<string> &vtBuff, string &parameter, long long msgid)
{
	string talk_id = vtBuff[1];
	string dstaddr = vtBuff[2];	
	string template_code = vtBuff[3];
	string msg_body = vtBuff[4];
	
	Json::Value root;
	char cmsgid[32] = {0x00,};
	sprintf(cmsgid, "%lld", msgid);

	char cmsg_body[4096] = {0x00,};
	char cmsg_body_utf8[4096] = {0x00,};
	sprintf(cmsg_body, "%s", msg_body.c_str());
	euckrToUtf8(cmsg_body, cmsg_body_utf8, sizeof(cmsg_body_utf8));

//	cerr<<"cmsg_body_utf8:"<<cmsg_body_utf8<<endl;
	root["serial_number"] = cmsgid;
	root["sender_key"] = talk_id;
	root["phone_number"] = dstaddr;
	root["template_code"] = template_code;
	//root["message"] = msg_body;
	root["message"] = cmsg_body_utf8;
	root["response_method"] = "realtime";

	Json::FastWriter writer;
    parameter = writer.write(root);
}

void CAlimtalkApi::makeSmsRequestMsgBt(map<string,string> &_mapSend, string &parameter, long long msgid)
{
	string sender_key;
	string dstaddr;	
	string template_code;
	string button_name;
	string button_url;
	string msg_body;
	string res_method;
	string timeout;

	sender_key = _mapSend["sender_key"];
    dstaddr = _mapSend["dst_addr"];
    template_code = _mapSend["template_code"];
    button_name = _mapSend["button_name"];
    button_url = _mapSend["button_url"];
    msg_body = _mapSend["msg_body"];
    res_method = _mapSend["res_method"];
	if(res_method.size() <= 0)
		res_method = "realtime";

	timeout	= _mapSend["timeout"];
	if(timeout.size() <= 0)
	{
		timeout = "10";
	}
	
	Json::Value root;
	
	char cmsgid[32] = {0x00,};
	sprintf(cmsgid, "%lld", msgid);

	char ctmpl_cd[64] = {0x00,};
    char ctmpl_cd_utf8[64] = {0x00,};
    sprintf(ctmpl_cd, "%s", template_code.c_str());
    euckrToUtf8(ctmpl_cd, ctmpl_cd_utf8, sizeof(ctmpl_cd_utf8));

	char cmsg_body[4096] = {0x00,};
	char cmsg_body_utf8[4096] = {0x00,};
	sprintf(cmsg_body, "%s", msg_body.c_str());
	euckrToUtf8(cmsg_body, cmsg_body_utf8, sizeof(cmsg_body_utf8));

	root["serial_number"] = cmsgid;
	root["sender_key"] = sender_key;
	root["phone_number"] = dstaddr;
	root["template_code"] = ctmpl_cd_utf8;
	root["message"] = cmsg_body_utf8;
	root["response_method"] = res_method;
	root["timeout"] = atoi(timeout.c_str());

	if(button_name.size() > 0)
	{
		Json::Value attachment;

		char cbutton_name[64] = {0x00,};
		char cbutton_name_utf8[64] = {0x00,};
		sprintf(cbutton_name, "%s", button_name.c_str());
		euckrToUtf8(cbutton_name, cbutton_name_utf8, sizeof(cbutton_name_utf8));

		Json::Value button;
		button["name"] = cbutton_name_utf8;
		button["url"] = button_url;
		attachment["button"] = button;
		root["attachment"] = attachment;
	}
	Json::FastWriter writer;
    parameter = writer.write(root);
}



//void CAlimtalkApi::makeSmsRequestMsg_V3(map<string,string> &_mapSend, string &parameter, long long msgid, string &button_gb)
void CAlimtalkApi::makeSmsRequestMsg_V3(map<string,string> &_mapSend, string &parameter, long long msgid)
{
	string sender_key;
	string dstaddr;	
	string template_code;
	string button_name;
	string button_url;
	string button_data;
	string msg_body;
	string res_method;
	string timeout;
	string title;
	char ctimeout[5+1];
	
	memset(ctimeout,0x00,sizeof(ctimeout));
		 
	string button_data_tmp;
	
	sender_key = _mapSend["sender_key"];
    dstaddr = _mapSend["dst_addr"];
    template_code = _mapSend["template_code"];
    button_name = _mapSend["button_name"];
    button_url = _mapSend["button_url"];
    button_data = _mapSend["button"];
    msg_body = _mapSend["msg_body"];
    res_method = _mapSend["res_method"];
    title = _mapSend["title"];
    
	if(res_method.size() <= 0)
		res_method = "realtime";

	if(res_method == "polling")
	{
		int nTimeout = 0;
		
		nTimeout = atoi(_mapSend["timeout"].c_str());
		
		
		if(nTimeout >= 10 && nTimeout <= 86400)
		{
			sprintf(ctimeout,"%d",nTimeout);
		
			timeout	= ctimeout;
		}
		else
		{
			timeout = "10";
		}
		
	}else{
		timeout	= _mapSend["timeout"];
		
		if(timeout.size() <= 0)
		{
			timeout = "10";
		}
	}
	
	Json::Value root;
	
	//char cmsgid[32] = {0x00,};
	//char cmsgid[39] = {0x00,};
	char cmsgid[39];
	memset(cmsgid,0x00,sizeof(cmsgid));
	//sprintf(cmsgid, "%lld", msgid);
	
	char	pch[30];
	struct timespec tmv;
	struct tm	tp;

	clock_gettime(CLOCK_REALTIME, &tmv);
	localtime_r(&tmv.tv_sec, &tp);

	memset(pch				,0x00		,sizeof(pch));	
	sprintf(pch, "%04d%02d%02d%02d%02d%02d%09d"
				,tp.tm_year+1900
				,tp.tm_mon+1
				,tp.tm_mday
				,tp.tm_hour
				,tp.tm_min
				,tp.tm_sec
				,(int)tmv.tv_nsec
				);

		
	sprintf(cmsgid,"%.8s-%lld", pch, msgid);

	char ctmpl_cd[64];
    char ctmpl_cd_utf8[64];
    
    memset(ctmpl_cd,0x00,sizeof(ctmpl_cd));
    memset(ctmpl_cd_utf8,0x00,sizeof(ctmpl_cd_utf8));
    
	//20180829 ctmpl_cd sprintf -> snprintf
    snprintf(ctmpl_cd, sizeof(ctmpl_cd),"%s", template_code.c_str());
    euckrToUtf8(ctmpl_cd, ctmpl_cd_utf8, sizeof(ctmpl_cd_utf8));

	//char cmsg_body[4096] = {0x00,};
	//char cmsg_body_utf8[4096] = {0x00,};
	char cmsg_body[4096];
	char cmsg_body_utf8[4096];
	
	memset(cmsg_body,0x00,sizeof(cmsg_body));
    memset(cmsg_body_utf8,0x00,sizeof(cmsg_body_utf8));
	//sprintf(cmsg_body, "%s", msg_body.c_str());
    //20180829 cmsg_body strcpy -> strncpy
	//strcpy(cmsg_body,msg_body.c_str());
	strncpy(cmsg_body,msg_body.c_str(),sizeof(cmsg_body)-1);
	euckrToUtf8(cmsg_body, cmsg_body_utf8, sizeof(cmsg_body_utf8));

	// AT : normal alimtalk
	// AI : image alimtalk
	// default is AT
	root["message_type"] = "AT";

	// 2021.0930 22:38 add shkim
//	if 	(!strcmp(ctmpl_cd_utf8,"BCD0000009")) root["message_type"] = "AT";
//	else if (!strcmp(ctmpl_cd_utf8,"WOC0000017")) root["message_type"] = "AT";


	// shkim template code add 2021.09.29	
	if(strcmp(ctmpl_cd_utf8,"BCV0000397") == 0 || strcmp(ctmpl_cd_utf8,"BBC0000046") == 0)
	{
		root["message_type"] = "AI";
	}
	else if (!strcmp(ctmpl_cd_utf8,"BCB0000003")) // 2021.09.27 BC 소비지원금
	{
		root["message_type"] = "AI";
	}
	else if (!strcmp(ctmpl_cd_utf8,"WOC0000014")) // 2021.09.29
	{
		root["message_type"] = "AI";
	}
	else if (!strcmp(ctmpl_cd_utf8,"WOC0000013")) // 2021.09.30
	{
		root["message_type"] = "AI";
	}
	else if (!strcmp(ctmpl_cd_utf8,"WOC0000015")) // 2021.09.30
	{
		root["message_type"] = "AI";
	}
	else if (!strcmp(ctmpl_cd_utf8,"BCC0000015")) // 2021.09.30
	{
		root["message_type"] = "AI";
	}
	else
	{
		root["message_type"] = "AT";
	}	
	
	root["serial_number"] = cmsgid;
	root["sender_key"] = sender_key;
	root["phone_number"] = dstaddr;
	root["template_code"] = ctmpl_cd_utf8;
	root["message"] = cmsg_body_utf8;
	root["response_method"] = res_method;
	root["timeout"] = atoi(timeout.c_str());
	
	if(title.size() > 0)
	{
		char ctitle[50+1];
		char ctitle_utf8[100+1];
	
		memset(ctitle,0x00,sizeof(ctitle));
  	memset(ctitle_utf8,0x00,sizeof(ctitle_utf8));
  
  	strncpy(ctitle,title.c_str(),sizeof(ctitle)-1);
		euckrToUtf8(ctitle, ctitle_utf8, sizeof(ctitle_utf8));
		
		root["title"] = ctitle_utf8;
	}
	
	if(button_name.size() > 0 && button_data.size() == 0)
	{
		Json::Value attachment;

		//char cbutton_type[2] = {0x00,};
		//char cbutton_type[3] = {0x00,};
		char cbutton_type[3];
		memset(cbutton_type,0x00,sizeof(cbutton_type));
		//char cbutton_type_utf8[2] = {0x00,};
		//char cbutton_name[64] = {0x00,};
		char cbutton_name[64];
		memset(cbutton_name,0x00,sizeof(cbutton_name));
		//char cbutton_name_utf8[64] = {0x00,};
		//char cbutton_name_utf8[128] = {0x00,};
		char cbutton_name_utf8[128];
		memset(cbutton_name_utf8,0x00,sizeof(cbutton_name_utf8));
		//char cbutton_url_pc[2]={0x00,};
		char cbutton_url_pc[2];
		memset(cbutton_url_pc,0x00,sizeof(cbutton_url_pc));
		
		if(button_url.length()>0)
		{
			//strcpy(cbutton_type, "WL");
			strncpy(cbutton_type, "WL", sizeof(cbutton_type)-1);
		}
		else
		{
			//strcpy(cbutton_type, "DS");
			strncpy(cbutton_type, "DS", sizeof(cbutton_type)-1);
		}
		//euckrToUtf8(cbutton_type, cbutton_type_utf8, sizeof(cbutton_type_utf8));
		
		//20180829 cbutton_name sprintf -> snprintf
		//sprintf(cbutton_name, "%s", button_name.c_str());
		snprintf(cbutton_name,sizeof(cbutton_name),"%s", button_name.c_str());
		euckrToUtf8(cbutton_name, cbutton_name_utf8, sizeof(cbutton_name_utf8));

		Json::Value button;
		button["name"] = cbutton_name_utf8;
		//button["type"] = cbutton_type_utf8;
		button["type"] = cbutton_type;
		if(button_url.length()>0)
		{
			char cbutton_url[300];
			char cbutton_url_utf8[600];
			memset(cbutton_url,0x00,sizeof(cbutton_url));
    		memset(cbutton_url_utf8,0x00,sizeof(cbutton_url_utf8));
			strncpy(cbutton_url,button_url.c_str(),sizeof(cbutton_url)-1);
			euckrToUtf8(cbutton_url, cbutton_url_utf8, sizeof(cbutton_url_utf8));
		
			button["url_pc"] = cbutton_url_pc;
			button["url_mobile"] = cbutton_url_utf8;
		}
		attachment["button"].append(button);
		root["attachment"] = attachment;
		
		//button_gb = "0";
		Json::FastWriter writer;
    	parameter = writer.write(root);
		
	}else if(button_data.size() > 0 && button_name.size() == 0)
	{
		Json::Value attachment;
		Json::Reader reader;
		
		//char cbutton_data[4100+1] = {0x00,};
		char cbutton_data[4100+1];
		memset(cbutton_data,0x00,sizeof(cbutton_data));
		//20180829 cbutton_data_utf8 4100+1 -> 8192
		//char cbutton_data_utf8[8192] = {0x00,};
		char cbutton_data_utf8[8192];
		memset(cbutton_data_utf8,0x00,sizeof(cbutton_data_utf8));
		string button_data_format;
		
		button_data_format = "{\"attachment\":{\"button\":[";
		button_data_format += button_data;
		button_data_format += "]},";
		
		//sprintf(cbutton_data, "%s", button_data_format.c_str());
		//20180829 cbutton_data strcpy -> strncpy
		//strcpy(cbutton_data, button_data_format.c_str());
		strncpy(cbutton_data, button_data_format.c_str(), sizeof(cbutton_data)-1);
		
		//sprintf(cbutton_data, "%s", button_data.c_str());
		euckrToUtf8(cbutton_data, cbutton_data_utf8, sizeof(cbutton_data_utf8));
		
		button_data_tmp = cbutton_data_utf8;
		
		//button_gb = "1";
		Json::FastWriter writer;
		string parameter_tmp;
		
    	parameter_tmp = writer.write(root);
    	RemoveFirst((char*)parameter_tmp.c_str());	
    	button_data_tmp += parameter_tmp.c_str();
    	parameter = button_data_tmp.c_str();
		
	}else{
		//button_gb = "0";
		Json::FastWriter writer;
    	parameter = writer.write(root);		
	}
		
	/*if(button_data.size() > 0)
	{
		button_gb = "1";
		Json::FastWriter writer;
		string parameter_tmp;
		
    	parameter_tmp = writer.write(root);
    	RemoveFirst((char*)parameter_tmp.c_str());	
    	button_data_tmp += parameter_tmp;
    	parameter = button_data_tmp.c_str();
	}else{
		button_gb = "0";
		Json::FastWriter writer;
    	parameter = writer.write(root);
		
	}*/
	//cout<<"parameter :"<<parameter<<endl;
}


void CAlimtalkApi::makeSmsRequestMsg_V4(map<string,string> &_mapSend, string &parameter, long long msgid)
{
	string sender_key;
	string dstaddr;	
	string template_code;
	string button_name;
	string button_url;
	string button_data;
	string msg_body;
	string res_method;
	string timeout;
	string title;
	string cur_type;
	string price;
	string message_type;
	string kko_header;
	string kko_attachment; //button item
	string kko_supplement;
	char ctimeout[5+1];
	double dPrice = 0;
	
	memset(ctimeout,0x00,sizeof(ctimeout));	
	 
	string button_data_tmp;
		
	string kko_attachment_data_tmp;
		 
	string supplement_data_tmp;
	string messageKey;
	string asyncSend;
	
	sender_key = _mapSend["sender_key"];
	//messageKey text(60) - msg_key
	messageKey = _mapSend["msg_key"];
    dstaddr = _mapSend["dst_addr"];
    template_code = _mapSend["template_code"];
    //button_name = _mapSend["button_name"];
    //button_url = _mapSend["button_url"];
    button_data = _mapSend["button"];
    msg_body = _mapSend["msg_body"];
	asyncSend = _mapSend["async"];
    //res_method = _mapSend["res_method"];
    //title = _mapSend["title"];
    //price = _mapSend["price"];
    //cur_type = _mapSend["cur_type"];
    //message_type = _mapSend["message_type"];
    //kko_header = _mapSend["kko_header"];
	//kko_attachment = _mapSend["attachment"];
	//kko_supplement = _mapSend["supplement"];   
        
	//if(res_method.size() <= 0)
	//	res_method = "realtime";

	//if(asyncSend.size() <= 0)
	//	asyncSend = "Y";

	/**if(res_method == "polling")
	{
		int nTimeout = 0;
		
		nTimeout = atoi(_mapSend["timeout"].c_str());
		
		
		if(nTimeout >= 10 && nTimeout <= 86400)
		{
			sprintf(ctimeout,"%d",nTimeout);
		
			timeout	= ctimeout;
		}
		else
		{
			timeout = "10";
		}
		
	}else{
		timeout	= _mapSend["timeout"];
		
		if(timeout.size() <= 0)
		{
			timeout = "10";
		}
	}**/
	
	Json::Value root;

	/**
	//char cmsgid[32] = {0x00,};
	//char cmsgid[39] = {0x00,};
	char cmsgid[39];
	memset(cmsgid,0x00,sizeof(cmsgid));
	//sprintf(cmsgid, "%lld", msgid);
	
    char	pch[30];
	struct timespec tmv;
	struct tm	tp;

	clock_gettime(CLOCK_REALTIME, &tmv);
	localtime_r(&tmv.tv_sec, &tp);

	memset(pch				,0x00		,sizeof(pch));	
	sprintf(pch, "%04d%02d%02d%02d%02d%02d%09d"
				,tp.tm_year+1900,tp.tm_mon+1,tp.tm_mday,tp.tm_hour
				,tp.tm_min,tp.tm_sec,(int)tmv.tv_nsec	);
		
	sprintf(cmsgid,"%.8s-%lld", pch, msgid);
	**/

	char ctmpl_cd[64];
    char ctmpl_cd_utf8[64];
    
    memset(ctmpl_cd,0x00,sizeof(ctmpl_cd));
    memset(ctmpl_cd_utf8,0x00,sizeof(ctmpl_cd_utf8));
    
	//20180829 ctmpl_cd sprintf -> snprintf
    snprintf(ctmpl_cd, sizeof(ctmpl_cd),"%s", template_code.c_str());
    euckrToUtf8(ctmpl_cd, ctmpl_cd_utf8, sizeof(ctmpl_cd_utf8));
    
    /**if(kko_header.size() > 0)
	{
			char ckko_header[16];
    	char ckko_header_utf8[32];
    
   	 memset(ckko_header,0x00,sizeof(ckko_header));
   	 memset(ckko_header_utf8,0x00,sizeof(ckko_header_utf8));
    
			//20180829 ctmpl_cd sprintf -> snprintf
   	 snprintf(ckko_header, sizeof(ckko_header),"%s", kko_header.c_str());
   	 euckrToUtf8(ckko_header, ckko_header_utf8, sizeof(ckko_header));
   	 
   	 root["header"] = ckko_header_utf8;
	
   	}**/

	//char cmsg_body[4096] = {0x00,};
	//char cmsg_body_utf8[4096] = {0x00,};
	char cmsg_body[4096];
	char cmsg_body_utf8[4096];
	
	memset(cmsg_body,0x00, sizeof(cmsg_body));
    memset(cmsg_body_utf8, 0x00, sizeof(cmsg_body_utf8));
	//sprintf(cmsg_body, "%s", msg_body.c_str());
    //20180829 cmsg_body strcpy -> strncpy
	//strcpy(cmsg_body,msg_body.c_str());
#if (DEBUG >= 5)
	cout << "msg_body : " << msg_body.c_str() << endl;
#endif

	strncpy(cmsg_body, msg_body.c_str(), sizeof(cmsg_body)-1);
	euckrToUtf8(cmsg_body, cmsg_body_utf8, sizeof(cmsg_body_utf8));
	
	//root["serial_number"] = cmsgid;
	root["messageKey"] = messageKey;
	
	//root["sender_key"] = sender_key;
	root["auth_code"] = sender_key;
	
	//root["phone_number"] = dstaddr;
	root["phoneNumber"] = dstaddr;
	
	//root["template_code"] = ctmpl_cd_utf8;
	root["templateCode"] = ctmpl_cd_utf8;

	Json::Value templateParamsObj;
	Json::Reader reader;	
	
	if (reader.parse(cmsg_body_utf8, templateParamsObj)) {
		root["templateParams"] = templateParamsObj;
	}	
	else {		
		cout << "msg_body Json parsing error : " << cmsg_body_utf8 << endl;
	}	
	
	//root["templateParams"] = cmsg_body_utf8;

	//root["attachments"] = button_data;

	if (asyncSend.size() > 0)
		root["asyncSend"] = asyncSend;
	
	//root["response_method"] = res_method;
	//root["timeout"] = atoi(timeout.c_str());
	
    //if(button_data.size() > 0 && button_name.size() == 0)
    if(button_data.size() > 0)		
	{
		//Json::Value attachment;
		//Json::Reader reader;
		
		//char cbutton_data[4100+1] = {0x00,};
		char cbutton_data[4100+1];
		memset(cbutton_data,0x00,sizeof(cbutton_data));
		//20180829 cbutton_data_utf8 4100+1 -> 8192
		//char cbutton_data_utf8[8192] = {0x00,};
		char cbutton_data_utf8[8192];
		memset(cbutton_data_utf8,0x00,sizeof(cbutton_data_utf8));
		string button_data_format;
		
		//button_data_format = "{\"attachment\":{\"button\":[";
		button_data_format = "{\"attachments\":{\"buttons\":[";
		button_data_format += button_data;
		button_data_format += "]},";
		
		//sprintf(cbutton_data, "%s", button_data_format.c_str());
		//20180829 cbutton_data strcpy -> strncpy
		//strcpy(cbutton_data, button_data_format.c_str());
		strncpy(cbutton_data, button_data_format.c_str(), sizeof(cbutton_data)-1);
		
		//sprintf(cbutton_data, "%s", button_data.c_str());
		euckrToUtf8(cbutton_data, cbutton_data_utf8, sizeof(cbutton_data_utf8));
		
		button_data_tmp = cbutton_data_utf8;
		
		//button_gb = "1";
		Json::FastWriter writer;
		string parameter_tmp;
		
    	parameter_tmp = writer.write(root);
    	RemoveFirst((char*)parameter_tmp.c_str());	
		
    	//if(supplement_data_tmp.size() > 0)
    	//{
    	//	kko_attachment_data_tmp += supplement_data_tmp.c_str();
    	//}
		
    	button_data_tmp += parameter_tmp.c_str();
    	parameter = button_data_tmp.c_str();
    	//parameter = button_data_tmp;
		
	}	
	else {
		Json::FastWriter writer;
		//button_gb = "0";
		/**if (supplement_data_tmp.size() > 0) {
	
			string parameter_tmp;
			string supplement_last_data_tmp;
	
			parameter_tmp = writer.write(root);
			RemoveFirst((char *) parameter_tmp.c_str());
			
			supplement_last_data_tmp += parameter_tmp.c_str();
			
			parameter = "{";
			parameter += supplement_last_data_tmp.c_str();
		}
		else
		**/
		{
			parameter = writer.write(root);
		}
	
	}	

	#if (DEBUG >= 5)
	cout<<"parameter :"<<parameter<<endl;
	#endif
}

void CAlimtalkApi::makePollingRequestMsg(string channelKey, string &parameter)
{
	string channel_key;
	
	channel_key = channelKey.c_str();
    		
	Json::Value root;
	
	root["channel_key"] = channel_key.c_str();
	
	Json::FastWriter writer;
   	parameter = writer.write(root);		
}

int CAlimtalkApi::parsingResponse(string response, ST_TALK_RES &res)
{
	Json::Value root;
	Json::Reader reader;

	bool parsingSuccessful = reader.parse(response, root);
	if(!parsingSuccessful)
	{
		cout<<"Failed to parse response msg"<<endl;
		return -1;
	}

	res.success = root.get("success", "").asString();
	res.transmissionId = root.get("transmissionId", "").asString();

	//res.received_at = root.get("received_at", "").asString();
	//res.code = root.get("code", "").asString();
	res.resultCode = root.get("resultCode", "").asString();

	//if(res.code != "0000")
	if(res.resultCode != "0000")
	{
		//res.message = root.get("message", "").asString();		
		res.resultMessage = root.get("resultMessage", "").asString();
	}
	
	return 0;
}

int CAlimtalkApi::parsingPollingMsgResponse(string response, ST_TALK_POLLING_RES &res\
	                             , ST_POLLING_SUCCESS * _success, ST_POLLING_FAIL * _fail\
	                             , int & _successSize, int & _failSize)
{
	Json::Value root;
	Json::Reader reader;

  //////////////////////////////////////////////////
  // parse polling response msg
	bool parsingSuccessful = reader.parse(response, root);
	if(!parsingSuccessful)
	{
		cout<<"Failed to parse polling response msg"<<endl;
		return -1;
	}	

	res.code = root.get("code", "").asString();
	if(res.code != "0000")
	{
		res.message = root.get("message", "").asString(); 	
	}

	res.response_id  = root.get("response_id",  "").asString();
	res.responsed_at = root.get("responsed_at", "").asString();

	int msgCount = 0;

  //////////////////////////////////////////////////
  // parse json array(response key in response Json)
	const Json::Value responseRoot_t = root["response"];
	const Json::Value & success_msgs = responseRoot_t["success"];
	for( Json::ValueConstIterator it = success_msgs.begin(); \
	      it != success_msgs.end(); ++it)
  {
  	const Json::Value & success = *it;

  	_success[ msgCount].sn          = success.get( "serial_number","").asString();
  	_success[ msgCount].status      = success.get( "status","").asString();
  	_success[ msgCount].received_at = success.get( "received_at","").asString();
  	++msgCount; //  check success length
  }	

	_successSize = msgCount;
	msgCount = 0;

	const Json::Value & fail_msgs = responseRoot_t["fail"];
	for( Json::ValueConstIterator it = fail_msgs.begin(); \
	      it != fail_msgs.end(); ++it)
  {
  	const Json::Value & fail = *it;

  	_fail[ msgCount].sn          = fail.get( "serial_number","").asString();
  	_fail[ msgCount].status      = fail.get( "status","").asString();
  	_fail[ msgCount].received_at = fail.get( "received_at","").asString();
  	_fail[ msgCount].message     = fail.get( "message","").asString();
  	
  	++msgCount; // check fail length
  }
	_failSize = msgCount;

	return 0;
}

int CAlimtalkApi::euckrToUtf8(char *source, char *dest, int dest_size)
{
	iconv_t it;
	char *pout;
	size_t in_size, out_size;

	it = iconv_open("UTF-8", "EUC-KR");
	in_size = strlen(source);
	out_size = dest_size;
	pout = dest;

	if(iconv(it, &source, &in_size, &pout, &out_size) < 0)
		return -1;


	iconv_close(it);
	
	//20180829 block cout 
	//cout<<"source : \n"<<source<<endl;
	//cout<<"dest : \n"<<dest<<endl;
	
	return (pout - dest);	
}

void CAlimtalkApi::makeDateString(const string orgDate, string &dateString)
{
	char yyyy[4+1]; char mm[2+1]; char dd[2+1];
	char hh[2+1]; char mi[2+1]; char ss[2+1];

	int ret = 0;
	ret = sscanf(orgDate.c_str(), "%4s-%2s-%2s %2s:%2s:%2s", yyyy, mm, dd, hh, mi, ss);
	if(ret < 0)
	{
		throw -1;
	}

	dateString.append(yyyy);
	dateString.append(mm);
	dateString.append(dd);
	dateString.append(hh);
	dateString.append(mi);
	dateString.append(ss);

}


char *replaceAll(char *s, const char *olds, const char *news) {
  char *result, *sr;
  size_t i, count = 0;
  size_t oldlen = strlen(olds); if (oldlen < 1) return s;
  size_t newlen = strlen(news);


  if (newlen != oldlen) {
    for (i = 0; s[i] != '\0';) {
      if (memcmp(&s[i], olds, oldlen) == 0) count++, i += oldlen;
      else i++;
    }
  } else i = strlen(s);


  result = (char *) malloc(i + 1 + count * (newlen - oldlen));
  if (result == NULL) return NULL;


  sr = result;
  while (*s) {
    if (memcmp(s, olds, oldlen) == 0) {
      memcpy(sr, news, newlen);
      sr += newlen;
      s  += oldlen;
    } else *sr++ = *s++;
  }
  *sr = '\0';

  return result;
}

void RemoveFirst(char *buf)
{
    int i = 0;
    for (i = 1; buf[i]; i++)
    {
        buf[i - 1] = buf[i];
    }
    
    buf[i - 1] = '\0';
}


void RemoveEnd(char *buf)
{
    int i = 0;    
    while (buf[i])
    {
        i++;
    }
    
    buf[i - 1] = '\0';
}
