#include <stdio.h>
#include <stdlib.h>
#include <string.h>

#include "seed.h"
#include "KISA_SEED_CBC.h"
#include "base64.h"

BYTE  IV[16] = {0x056, 0x050, 0x020, 0x050, 0x075, 0x073, 0x068, 0x020, 0x053, 0x079, 0x073, 0x074, 0x065, 0x06D, 0x031, 0x032};
BYTE KEY[16] = {0x021, 0x040, 0x023, 0x024, 0x025, 0x076, 0x070, 0x05F, 0x070, 0x075, 0x073, 0x068, 0x031, 0x032, 0x033, 0x034};


int getEncryptSize(int len)
{
	int ret;
	if(len%16 == 0){
		ret = len;
	}else{
		ret = (len/16)*16;
	}

	return ret+16;
}

int getCipherTextSize(int len)
{
	return Base64encode_len(getEncryptSize(len));
}


void printHex(BYTE *byte, int len){
	int i = 0;
	for (i=0;i<len;i++)	{printf("%02X ",(BYTE)byte[i]);}
	printf("\n");
}

int Encrypt(BYTE *key, char *out, int outLen, char *in, int inLen)
{
	int ret = 0;
	int rc;
	int nCipherTextLen;
	char *pbszCipherText = NULL;

	
	/* CipherText 공간 할당 */
	pbszCipherText = (char*)malloc(getEncryptSize(inLen)+1);
	if(pbszCipherText == NULL){
		ret = -1;
		goto end;
	}

	/* SEED CBC 암호화 */
	nCipherTextLen = SEED_CBC_Encrypt( key, IV, (BYTE *)in, inLen, (BYTE *)pbszCipherText );
	if(nCipherTextLen <= 0){
		ret = -2;
		goto end;
	}
	
	/* Base64 인코딩 */
	rc = Base64encode(out, pbszCipherText, nCipherTextLen);
	if(rc <= 0){
		ret = -3;
		goto end;
	}
	
end:
	if(pbszCipherText != NULL) free(pbszCipherText);
	return ret;
}

int Decrypt(BYTE *key, char *out, int outLen, char *in, int inLen)
{
	int ret = 0;
	int nCipherTextLen;
	int nPlainTextLen;
	char *pbszCipherText = NULL;

	/* Base64 디코딩 */
	nCipherTextLen = Base64decode_len(in);
	pbszCipherText = (char*)malloc(nCipherTextLen);
	if(pbszCipherText == NULL){
		ret = -1;
		goto end;
	}
	memset(pbszCipherText, 0, nCipherTextLen);

	nCipherTextLen = Base64decode(pbszCipherText, in);
	if(nCipherTextLen < 0 || nCipherTextLen >= Base64decode_len(in)){
		ret -2;
		goto end;
	}
	pbszCipherText[nCipherTextLen] = 0;

	/* SEED CBC 복호화 */
	nPlainTextLen = SEED_CBC_Decrypt( key, IV, (BYTE *)pbszCipherText, nCipherTextLen, (BYTE *)out);
	if(nPlainTextLen <= 0){
		ret = -3;
		goto end;
	}

end:
	if(pbszCipherText != NULL) free(pbszCipherText);
	return ret;
}
