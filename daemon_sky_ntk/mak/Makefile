PROC=proc
CC=g++
COPY=cp
RM=rm
LINT=lint
CFLAGS = -g -std=gnu++11 -Wall -DDEBUG=5

DBSTRING=DEV_05_12G
DBID=neontk
DBPASS=dev_neontk05

ORG_D=/user/neontk/daemon_sky_ntk
BIN_D=${ORG_D}/bin
OBJ_D=${ORG_D}/obj
LIB_D=${ORG_D}/lib
INC_D=${ORG_D}/inc
SRC_D=${ORG_D}/src

EXT_LIB=/user/neontk/command_kskyb_ntk/obj/sms_ctrlsub++.o
EXT_INC=/user/neontk/command_kskyb_ntk/inc

KSLIBRARY_PATH=$(HOME)/library
KSLIBRARY_INC=$(HOME)/library

INCLUDE = -I$(INC_D) -I$(LIB_D)

ORALIB1 = ${ORACLE_HOME}/lib
ORALIB2 = ${ORACLE_HOME}/plsql/lib
ORALIB3 = ${ORACLE_HOME}/network/lib
ORA_INC = ${ORACLE_HOME}/precomp/public

INCLUDE = $(PRECOMPPUBLIC) -I$(INC_D) -I$(LIB_D) -I$(ORA_INC) -I/usr/include
LINKFLAGS = -L$(ORALIB1) -L$(ORALIB2) -L$(ORALIB3) -L$(ORA_INC)
ORALIB = -lclntsh
#LIBS = -lnsl -lsocket
#LIBS = -lnsl -lpthread -ldl 
LIBS = -lnsl -lpthread -ldl -lcrypto 

all: telco_sky_new_r

telco_sky_new_r: $(OBJ_D)/telco_sky_new.o $(OBJ_D)/Properties.o $(OBJ_D)/SocketTCP.o  $(OBJ_D)/ksbase64.o $(OBJ_D)/PacketCtrlSKY.o $(OBJ_D)/DatabaseORA.o $(OBJ_D)/myException.o
	${CC} $(CFLAGS) $^ $(EXT_LIB) -I$(EXT_INC) $(INCLUDE) $(LIBD) ${LINKFLAGS} ${SQL_INCLUDE} ${PROLDLIBS} ${MATHLIBS} ${LIBS} -lpthread $(ORALIB) -o $(BIN_D)/telco_sky_new_r_tmp

$(OBJ_D)/telco_sky_new.o: $(SRC_D)/telco_sky_new.cpp
	$(RM) -rf $(OBJ_D)/telco_sky_new.*
	$(COPY) $(SRC_D)/telco_sky_new.cpp $(OBJ_D)/telco_sky_new.pc
	$(PROC) MODE=ORACLE DBMS=V7 UNSAFE_NULL=YES iname=$(OBJ_D)/telco_sky_new.pc \
		include=$(INC_D) include=$(ORA_INC) \
		include=$(EXT_INC) CPP_SUFFIX=cpp CODE=CPP PARSE=NONE CTIMEOUT=3 \
		define=__sparc SQLCHECK=SEMANTICS userid=$(DBID)/$(DBPASS)@$(DBSTRING)
	$(CC) $(CFLAGS) -o $(OBJ_D)/telco_sky_new.o $(CFLAGS) -I${INCLUDE} -I$(EXT_INC) -c $(OBJ_D)/telco_sky_new.cpp

$(OBJ_D)/Properties.o: $(LIB_D)/Properties.cpp
	$(RM) -rf $(OBJ_D)/Properties.*
	$(CC) -o $@ $(CFLAGS) -I${INCLUDE} -I$(EXT_INC) -c $^

$(OBJ_D)/SocketTCP.o: $(LIB_D)/SocketTCP.cpp
	$(RM) -rf $(OBJ_D)/SocketTCP.*
	$(CC) -o $@ $(CFLAGS) -I${INCLUDE} -I$(EXT_INC) -c $^

$(OBJ_D)/PacketCtrlSKY.o: $(LIB_D)/PacketCtrlSKY.cpp
	$(RM) -rf $(OBJ_D)/PacketCtrlSKY.*
	$(CC) -o $@ $(CFLAGS) -I${INCLUDE} -I$(EXT_INC) ${LIBS} -lksbase64 -L$(KSLIBRARY_PATH) -c $^

$(OBJ_D)/DatabaseORA.o: $(LIB_D)/DatabaseORA.cpp
	$(RM) -rf $(OBJ_D)/DatabaseORA.*
	$(COPY) $(LIB_D)/DatabaseORA.cpp $(OBJ_D)/DatabaseORA.pc
	#$(PROC) MODE=ORACLE DBMS=V7 UNSAFE_NULL=YES iname=$(OBJ_D)/DatabaseORA.pc \
	$(PROC) iname=$(OBJ_D)/DatabaseORA.pc \
		include=$(INC_D) include=$(ORA_INC) \
		include=$(EXT_INC) THREADS=YES CPP_SUFFIX=cpp CODE=CPP PARSE=NONE CTIMEOUT=3 \
		define=__sparc SQLCHECK=SEMANTICS userid=$(DBID)/$(DBPASS)@$(DBSTRING)
	$(CC) $(CFLAGS) -o $(OBJ_D)/DatabaseORA.o $(CFLAGS) -I${INCLUDE} -I$(EXT_INC) -c $(OBJ_D)/DatabaseORA.cpp

$(OBJ_D)/myException.o: $(LIB_D)/myException.cpp
	$(RM) -rf $(OBJ_D)/myException.*
	$(CC) -o $@ $(CFLAGS) -I${INCLUDE} -I$(EXT_INC) -c $^

$(OBJ_D)/ksbase64.o: $(LIB_D)/ksbase64.cpp
	$(RM) -rf $(OBJ_D)/ksbase64.*
	$(CC) -o $@ $(CFLAGS) -I${INCLUDE} -I$(EXT_INC) -c $^
	
clean:
	rm  -rf $(OBJ_D)/*.o tp* $(OBJ_D)/*.lis $(OBJ_D)/*.pc $(OBJ_D)/*.cpp

install:
	mv $(BIN_D)/telco_sky_new_r_tmp $(BIN_D)/telco_ntk_sky
